package com.yyzs.app.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 管理后台应用启动类
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.yyzs.common",
    "com.yyzs.modules.system",
    "com.yyzs.modules.monitor",
    "com.yyzs.modules.file",
    "com.yyzs.modules.notification",
    "com.yyzs.modules.workflow",
    "com.yyzs.plugins.security",
    "com.yyzs.plugins.cache",
    "com.yyzs.plugins.logging",
    "com.yyzs.app.admin"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.system.mapper",
    "com.yyzs.modules.monitor.mapper",
    "com.yyzs.modules.file.mapper",
    "com.yyzs.modules.notification.mapper",
    "com.yyzs.modules.workflow.mapper"
})
public class AdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }
}
