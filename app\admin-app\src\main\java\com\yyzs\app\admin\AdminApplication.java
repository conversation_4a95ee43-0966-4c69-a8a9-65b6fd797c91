package com.yyzs.app.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 管理后台应用启动类
 *
 * 注意：模块和插件的扫描配置已通过自动配置机制实现
 * 只需要在 pom.xml 中添加相应的依赖即可自动加载对应的模块和插件
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.yyzs.common",
    "com.yyzs.app.admin"
})
public class AdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }
}
