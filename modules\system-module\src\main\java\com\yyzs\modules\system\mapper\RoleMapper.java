package com.yyzs.modules.system.mapper;

import com.yyzs.common.annotation.DataSource;
import com.yyzs.common.mapper.YYBaseMapper;
import com.yyzs.modules.system.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Repository
@Mapper
public interface RoleMapper extends YYBaseMapper<Role> {

    /**
     * 根据角色编码查询角色
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode} AND deleted = 0")
    Role findByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 查询启用的角色列表
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT * FROM sys_role WHERE status = 1 AND deleted = 0 ORDER BY sort_order ASC")
    List<Role> findEnabledRoles();

    /**
     * 根据用户ID查询角色列表
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.deleted = 0")
    List<Role> findByUserId(@Param("userId") String userId);

    /**
     * 检查角色编码是否存在
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_code = #{roleCode} AND deleted = 0")
    int checkRoleCodeExists(@Param("roleCode") String roleCode);

    /**
     * 检查角色编码是否存在（排除指定ID）
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_code = #{roleCode} AND id != #{id} AND deleted = 0")
    int checkRoleCodeExistsExcludeId(@Param("roleCode") String roleCode, @Param("id") String id);

    /**
     * 根据条件查询角色数量
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("<script>" +
            "SELECT COUNT(*) FROM sys_role WHERE deleted = 0 " +
            "<if test='status != null'>" +
            "  AND status = #{status}" +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  AND (role_name LIKE CONCAT('%', #{keyword}, '%') " +
            "       OR role_code LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if>" +
            "</script>")
    Long countByCondition(@Param("status") Integer status, @Param("keyword") String keyword);

    /**
     * 分页查询角色列表
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("<script>" +
            "SELECT * FROM sys_role WHERE deleted = 0 " +
            "<if test='status != null'>" +
            "  AND status = #{status}" +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  AND (role_name LIKE CONCAT('%', #{keyword}, '%') " +
            "       OR role_code LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if>" +
            "ORDER BY sort_order ASC, create_time DESC " +
            "OFFSET #{offset} LIMIT #{limit}" +
            "</script>")
    List<Role> findByPage(@Param("status") Integer status,
                         @Param("keyword") String keyword,
                         @Param("offset") Long offset,
                         @Param("limit") Long limit);

    /**
     * 更新角色状态
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Update("UPDATE sys_role SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") Integer status);

    /**
     * 批量删除角色（软删除）
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Update("<script>" +
            "UPDATE sys_role SET deleted = 1, update_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "  #{id}" +
            "</foreach>" +
            "</script>")
    int batchDelete(@Param("ids") List<String> ids);
}
