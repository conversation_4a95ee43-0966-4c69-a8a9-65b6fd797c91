package com.yyzs.agent.mapper;

import com.yyzs.agent.entity.AgentInfo;
import com.yyzs.common.annotation.DataSource;
import com.yyzs.common.mapper.YYBaseMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent信息Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Repository
@Mapper
public interface AgentInfoMapper extends YYBaseMapper<AgentInfo> {

    /**
     * 根据Agent ID查询Agent信息
     */
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM agent_info WHERE agent_id = #{agentId} AND deleted = 0")
    AgentInfo findByAgentId(@Param("agentId") String agentId);

    /**
     * 查询在线Agent列表
     */
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM agent_info WHERE status = 1 AND deleted = 0 ORDER BY last_heartbeat DESC")
    List<AgentInfo> findOnlineAgents();

    /**
     * 查询离线Agent列表
     */
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM agent_info WHERE status = 0 AND deleted = 0 ORDER BY last_heartbeat DESC")
    List<AgentInfo> findOfflineAgents();

    /**
     * 根据服务器IP查询Agent
     */
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM agent_info WHERE server_ip = #{serverIp} AND deleted = 0")
    List<AgentInfo> findByServerIp(@Param("serverIp") String serverIp);

    /**
     * 更新Agent状态
     */
    @DataSource(DataSource.DataSourceType.AGENT)
    @Update("UPDATE agent_info SET status = #{status}, last_heartbeat = #{lastHeartbeat}, update_time = NOW() WHERE agent_id = #{agentId}")
    int updateStatus(@Param("agentId") String agentId, @Param("status") Integer status, @Param("lastHeartbeat") LocalDateTime lastHeartbeat);

    /**
     * 更新Agent心跳时间
     */
    @Update("UPDATE agent_info SET last_heartbeat = #{lastHeartbeat}, update_time = NOW() WHERE agent_id = #{agentId}")
    int updateHeartbeat(@Param("agentId") String agentId, @Param("lastHeartbeat") LocalDateTime lastHeartbeat);

    /**
     * 批量更新离线Agent状态
     */
    @Update("UPDATE agent_info SET status = 0, update_time = NOW() WHERE last_heartbeat < #{timeThreshold} AND status != 0")
    int batchUpdateOfflineStatus(@Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 根据条件查询Agent数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM agent_info WHERE deleted = 0 " +
            "<if test='status != null'>" +
            "  AND status = #{status}" +
            "</if>" +
            "<if test='osType != null and osType != \"\"'>" +
            "  AND os_type = #{osType}" +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  AND (agent_name LIKE CONCAT('%', #{keyword}, '%') " +
            "       OR server_ip LIKE CONCAT('%', #{keyword}, '%') " +
            "       OR server_hostname LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if>" +
            "</script>")
    Long countByCondition(@Param("status") Integer status, 
                         @Param("osType") String osType, 
                         @Param("keyword") String keyword);

    /**
     * 分页查询Agent列表
     */
    @Select("<script>" +
            "SELECT * FROM agent_info WHERE deleted = 0 " +
            "<if test='status != null'>" +
            "  AND status = #{status}" +
            "</if>" +
            "<if test='osType != null and osType != \"\"'>" +
            "  AND os_type = #{osType}" +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  AND (agent_name LIKE CONCAT('%', #{keyword}, '%') " +
            "       OR server_ip LIKE CONCAT('%', #{keyword}, '%') " +
            "       OR server_hostname LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if>" +
            "ORDER BY last_heartbeat DESC " +
            "LIMIT #{offset}, #{limit}" +
            "</script>")
    List<AgentInfo> findByPage(@Param("status") Integer status,
                              @Param("osType") String osType,
                              @Param("keyword") String keyword,
                              @Param("offset") Long offset,
                              @Param("limit") Long limit);

    /**
     * 查询超时未心跳的Agent
     */
    @Select("SELECT * FROM agent_info WHERE last_heartbeat < #{timeThreshold} AND status = 1 AND deleted = 0")
    List<AgentInfo> findTimeoutAgents(@Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 根据标签查询Agent
     */
    @Select("SELECT * FROM agent_info WHERE tags LIKE CONCAT('%', #{tag}, '%') AND deleted = 0")
    List<AgentInfo> findByTag(@Param("tag") String tag);

    /**
     * 统计Agent状态分布
     */
    @Select("SELECT status, COUNT(*) as count FROM agent_info WHERE deleted = 0 GROUP BY status")
    @Results({
        @Result(column = "status", property = "status"),
        @Result(column = "count", property = "count")
    })
    List<AgentStatusCount> countByStatus();

    /**
     * Agent状态统计内部类
     */
    class AgentStatusCount {
        private Integer status;
        private Long count;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }
    }
}
