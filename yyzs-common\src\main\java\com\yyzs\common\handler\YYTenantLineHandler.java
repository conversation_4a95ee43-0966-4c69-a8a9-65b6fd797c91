package com.yyzs.common.handler;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.yyzs.common.config.TenantProperties;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * MyBatis-Plus多租户处理器
 * 自动在SQL中添加租户条件
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Slf4j
@Component
public class YYTenantLineHandler implements TenantLineHandler {

    @Autowired
    private TenantProperties tenantProperties;

    /**
     * 获取租户ID
     * 从当前上下文中获取租户ID
     */
    @Override
    public Expression getTenantId() {
        // 从ThreadLocal或Spring Security上下文中获取租户ID
        String tenantId = getCurrentTenantId();
        log.debug("当前租户ID: {}", tenantId);
        return new StringValue(tenantId);
    }

    /**
     * 获取租户字段名
     */
    @Override
    public String getTenantIdColumn() {
        return tenantProperties.getTenantIdColumn();
    }

    /**
     * 根据表名判断是否忽略拼接多租户条件
     */
    @Override
    public boolean ignoreTable(String tableName) {
        // 如果多租户功能未启用，忽略所有表
        if (!tenantProperties.isEnabled()) {
            return true;
        }

        boolean ignore = tenantProperties.getIgnoreTables().contains(tableName.toLowerCase());
        if (ignore) {
            log.debug("表 {} 忽略多租户处理", tableName);
        }
        return ignore;
    }

    /**
     * 获取当前租户ID
     * 这里需要根据实际业务场景实现
     */
    private String getCurrentTenantId() {
        // 方式1: 从ThreadLocal获取
        String tenantId = TenantContextHolder.getTenantId();
        if (tenantId != null) {
            return tenantId;
        }

        // 方式2: 从Spring Security上下文获取
        // 可以根据实际情况实现

        // 方式3: 从HTTP请求头获取
        // 可以通过RequestContextHolder获取

        // 默认租户ID
        return tenantProperties.getDefaultTenantId();
    }
}
