package com.yyzs.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 多租户配置属性
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@Component
@ConfigurationProperties(prefix = "yyzs.tenant")
public class TenantProperties {

    /**
     * 是否启用多租户
     */
    private boolean enabled = true;

    /**
     * 租户字段名
     */
    private String tenantIdColumn = "tenant_id";

    /**
     * 忽略多租户的表名列表
     */
    private List<String> ignoreTables = Arrays.asList(
        "sys_tenant",           // 租户表本身
        "sys_tenant_package",   // 租户套餐表
        "sys_config",          // 系统配置表
        "sys_dict_type",       // 字典类型表
        "sys_dict_data",       // 字典数据表
        "sys_log_login",       // 登录日志表
        "sys_log_operate"      // 操作日志表
    );

    /**
     * 默认租户ID
     */
    private String defaultTenantId = "default";
}
