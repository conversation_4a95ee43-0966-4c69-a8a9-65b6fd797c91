package com.yyzs.app.api;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * API应用启动类
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.yyzs.common",
    "com.yyzs.modules.system",
    "com.yyzs.modules.file",
    "com.yyzs.modules.workflow",
    "com.yyzs.agent",
    "com.yyzs.plugins.security",
    "com.yyzs.plugins.cache",
    "com.yyzs.plugins.logging",
    "com.yyzs.app.api"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.system.mapper",
    "com.yyzs.modules.file.mapper",
    "com.yyzs.modules.workflow.mapper",
    "com.yyzs.agent.mapper"
})
public class ApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApiApplication.class, args);
    }
}
