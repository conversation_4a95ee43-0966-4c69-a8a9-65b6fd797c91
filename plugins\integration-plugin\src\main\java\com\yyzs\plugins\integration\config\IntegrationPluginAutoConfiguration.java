package com.yyzs.plugins.integration.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;

/**
 * Integration插件自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.plugins.integration.service.IntegrationService")
@ComponentScan(basePackages = {
    "com.yyzs.plugins.integration.service",
    "com.yyzs.plugins.integration.config",
    "com.yyzs.plugins.integration.handler"
})
public class IntegrationPluginAutoConfiguration {
    
    // 可以在这里定义集成插件特定的Bean配置
    
}
