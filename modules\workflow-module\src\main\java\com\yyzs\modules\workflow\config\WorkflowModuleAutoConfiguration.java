package com.yyzs.modules.workflow.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;

/**
 * Workflow模块自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.modules.workflow.entity.WorkflowDefinition")
@ComponentScan(basePackages = {
    "com.yyzs.modules.workflow.service",
    "com.yyzs.modules.workflow.controller",
    "com.yyzs.modules.workflow.config"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.workflow.mapper"
})
public class WorkflowModuleAutoConfiguration {
    
    // 可以在这里定义工作流模块特定的Bean配置
    
}
