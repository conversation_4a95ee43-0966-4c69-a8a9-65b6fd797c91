package com.yyzs.common.handler;

import lombok.extern.slf4j.Slf4j;

/**
 * 租户上下文持有者
 * 使用ThreadLocal存储当前线程的租户信息
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Slf4j
public class TenantContextHolder {

    private static final ThreadLocal<String> TENANT_ID_HOLDER = new ThreadLocal<>();

    /**
     * 设置当前租户ID
     *
     * @param tenantId 租户ID
     */
    public static void setTenantId(String tenantId) {
        log.debug("设置租户ID: {}", tenantId);
        TENANT_ID_HOLDER.set(tenantId);
    }

    /**
     * 获取当前租户ID
     *
     * @return 租户ID
     */
    public static String getTenantId() {
        String tenantId = TENANT_ID_HOLDER.get();
        log.debug("获取租户ID: {}", tenantId);
        return tenantId;
    }

    /**
     * 清除当前租户ID
     */
    public static void clear() {
        log.debug("清除租户ID");
        TENANT_ID_HOLDER.remove();
    }

    /**
     * 判断是否设置了租户ID
     *
     * @return true-已设置，false-未设置
     */
    public static boolean hasTenantId() {
        return TENANT_ID_HOLDER.get() != null;
    }
}
