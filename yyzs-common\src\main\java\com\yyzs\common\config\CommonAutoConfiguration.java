package com.yyzs.common.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * Common模块自动配置类
 *
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.common.entity.BaseEntity")
@ComponentScan(
    basePackages = "com.yyzs.common",
    excludeFilters = @ComponentScan.Filter(
        type = FilterType.REGEX,
        pattern = ".*AutoConfiguration"
    )
)
@EntityScan(basePackages = {
    "com.yyzs.common.entity"
})
public class CommonAutoConfiguration {
    
    // 可以在这里定义通用模块特定的Bean配置
    
}
