package com.yyzs.modules.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyzs.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 系统监控指标实体类
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("system_metrics")
public class SystemMetrics extends BaseEntity {

    /**
     * 服务器标识
     */
    @TableField("server_id")
    private String serverId;

    /**
     * 指标类型（CPU、MEMORY、DISK、NETWORK等）
     */
    @TableField("metric_type")
    private String metricType;

    /**
     * 指标名称
     */
    @TableField("metric_name")
    private String metricName;

    /**
     * 指标值
     */
    @TableField("metric_value")
    private BigDecimal metricValue;

    /**
     * 指标单位
     */
    @TableField("metric_unit")
    private String metricUnit;

    /**
     * 采集时间
     */
    @TableField("collect_time")
    private LocalDateTime collectTime;

    /**
     * 标签（JSON格式）
     */
    @TableField("tags")
    private String tags;

    /**
     * 描述
     */
    @TableField("description")
    private String description;
}
