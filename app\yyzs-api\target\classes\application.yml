# API Application Configuration
spring:
  application:
    name: yy-zs-api-app
  
  profiles:
    active: dev
    include: common

# Server Configuration
server:
  port: 8082
  servlet:
    context-path: /api

# Application specific configuration
yyzs:
  app:
    name: "YY-ZS API Application"
    description: "RESTful API services"
    version: "1.0.0"
  
  # API specific configuration
  api:
    enabled: true
    rate-limit: 1000 # requests per minute
    max-request-size: 10MB
    timeout: 30000 # 30 seconds
    
  # API specific configuration (CORS is managed in common config)
    
  # Security configuration for API
  security:
    enabled: true
    ignore-urls:
      - /api/auth/login
      - /api/auth/register
      - /api/public/**
      - /swagger-ui/**
      - /v3/api-docs/**
      - /actuator/health

# API Documentation
springdoc:
  api-docs:
    enabled: true
    path: /api/v3/api-docs
  swagger-ui:
    enabled: true
    path: /api/swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha

# Logging configuration for API
logging:
  level:
    com.yyzs.app.api: debug
    com.yyzs.modules: debug
  file:
    name: logs/api-app.log

# Management endpoints for API
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /api/actuator
  endpoint:
    health:
      show-details: when-authorized
