# Admin Application Configuration
spring:
  application:
    name: yy-zs-admin-app

  profiles:
    active: dev
    include: common

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /api/admin

# Application specific configuration
yyzs:
  app:
    name: "YY-ZS Admin Application"
    description: "System management and administration"
    version: "1.0.0"
  
  # Admin specific features
  admin:
    enabled: true
    max-login-attempts: 5
    session-timeout: 3600
    
  # Security configuration for admin
  security:
    enabled: true
    ignore-urls:
      - /api/admin/auth/login
      - /api/admin/auth/register
      - /api/admin/auth/forgot-password
      - /swagger-ui/**
      - /v3/api-docs/**
      - /druid/**
      - /actuator/health

# Logging configuration for admin
logging:
  level:
    com.yyzs.app.admin: debug
    com.yyzs.modules.system: debug
  file:
    name: logs/admin-app.log

# Management endpoints for admin
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops
      base-path: /api/admin/actuator
  endpoint:
    health:
      show-details: always
