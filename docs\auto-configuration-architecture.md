# 自动配置架构设计

## 概述

本项目采用了基于 Spring Boot 自动配置机制的模块化架构，每个模块和插件都有自己的自动配置类，应用层只需要通过 Maven 依赖来选择需要的模块，无需手动配置扫描注解。

## 架构优势

### 1. 完全解耦
- 应用层不需要知道模块的内部包结构
- 模块自己负责自己的组件扫描和 Mapper 扫描
- 插件可以独立开发和部署

### 2. 自动化配置
- 通过 `@ConditionalOnClass` 实现按需加载
- 只有当模块的关键类存在时才会激活自动配置
- 避免了不必要的配置和扫描

### 3. 灵活组合
- 应用可以通过 pom.xml 依赖自由组合模块
- 添加新模块只需要添加依赖，无需修改启动类
- 支持模块的热插拔

## 实现机制

### 1. 自动配置类
每个模块都有一个 `*AutoConfiguration` 类：

```java
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.modules.system.entity.User")
@ComponentScan(basePackages = {"com.yyzs.modules.system.service", ...})
@MapperScan(basePackages = {"com.yyzs.modules.system.mapper"})
public class SystemModuleAutoConfiguration {
    // 模块特定的Bean配置
}
```

### 2. 自动配置注册
通过 `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 文件注册：

```
com.yyzs.modules.system.config.SystemModuleAutoConfiguration
```

### 3. 条件化加载
使用 `@ConditionalOnClass` 确保只有当模块的关键类存在时才激活配置。

## 模块配置详情

### 业务模块

#### System Module (系统模块)
- **自动配置类**: `SystemModuleAutoConfiguration`
- **条件类**: `com.yyzs.modules.system.entity.User`
- **扫描包**: service, controller, config, mapper

#### Monitor Module (监控模块)
- **自动配置类**: `MonitorModuleAutoConfiguration`
- **条件类**: `com.yyzs.modules.monitor.entity.SystemMetrics`
- **扫描包**: service, controller, config, task, mapper

#### File Module (文件模块)
- **自动配置类**: `FileModuleAutoConfiguration`
- **条件类**: `com.yyzs.modules.file.entity.FileInfo`
- **扫描包**: service, controller, config, mapper

#### Notification Module (通知模块)
- **自动配置类**: `NotificationModuleAutoConfiguration`
- **条件类**: `com.yyzs.modules.notification.entity.NotificationMessage`
- **扫描包**: service, controller, config, mapper

#### Workflow Module (工作流模块)
- **自动配置类**: `WorkflowModuleAutoConfiguration`
- **条件类**: `com.yyzs.modules.workflow.entity.WorkflowDefinition`
- **扫描包**: service, controller, config, mapper

### Agent 模块

#### Agent Module
- **自动配置类**: `AgentAutoConfiguration`
- **条件类**: `com.yyzs.agent.entity.AgentInfo`
- **扫描包**: service, controller, config, task, mapper

### 插件模块

#### Security Plugin (安全插件)
- **自动配置类**: `SecurityPluginAutoConfiguration`
- **条件类**: `com.yyzs.plugins.security.service.SecurityService`
- **扫描包**: service, config, filter, handler

#### Cache Plugin (缓存插件)
- **自动配置类**: `CachePluginAutoConfiguration`
- **条件类**: `com.yyzs.plugins.cache.service.CacheService`
- **扫描包**: service, config, aspect

#### Logging Plugin (日志插件)
- **自动配置类**: `LoggingPluginAutoConfiguration`
- **条件类**: `com.yyzs.plugins.logging.service.LoggingService`
- **扫描包**: service, config, aspect

#### Integration Plugin (集成插件)
- **自动配置类**: `IntegrationPluginAutoConfiguration`
- **条件类**: `com.yyzs.plugins.integration.service.IntegrationService`
- **扫描包**: service, config, handler

## 应用配置

### 简化的启动类
现在应用启动类非常简洁：

```java
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.yyzs.common",
    "com.yyzs.app.admin"  // 只扫描应用自己的包
})
public class AdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }
}
```

### 通过依赖控制模块加载
在 `pom.xml` 中添加需要的模块依赖：

```xml
<!-- Admin App 需要的模块 -->
<dependency>
    <groupId>com.yyzs</groupId>
    <artifactId>yy-zs-system-module</artifactId>
</dependency>
<dependency>
    <groupId>com.yyzs</groupId>
    <artifactId>yy-zs-monitor-module</artifactId>
</dependency>
<!-- 其他模块... -->
```

## 使用指南

### 1. 添加新模块
1. 创建模块的自动配置类
2. 创建 `AutoConfiguration.imports` 文件
3. 在需要的应用 pom.xml 中添加依赖

### 2. 移除模块
1. 从应用的 pom.xml 中移除依赖
2. 重新编译启动即可

### 3. 开发新插件
1. 按照插件规范创建自动配置类
2. 实现条件化加载
3. 注册自动配置

## 注意事项

### 1. 条件类选择
- 选择模块中最核心、最稳定的类作为条件类
- 避免选择可能被重构的类

### 2. 包扫描范围
- 只扫描模块内部的包
- 避免扫描过于宽泛的包路径

### 3. 依赖管理
- 确保模块间的依赖关系清晰
- 避免循环依赖

## 验证方法

### 1. 检查自动配置是否生效
启动应用时添加调试参数：
```bash
java -jar app.jar --debug
```

### 2. 查看加载的自动配置
在日志中查找 "CONDITIONS EVALUATION REPORT" 部分。

### 3. 测试模块功能
确保各模块的功能正常工作，Mapper 能正常注入。

## 总结

这种自动配置架构实现了真正的模块化和插件化，应用层只需要关心业务逻辑，模块的技术细节完全封装在模块内部。这大大提高了系统的可维护性和扩展性。
