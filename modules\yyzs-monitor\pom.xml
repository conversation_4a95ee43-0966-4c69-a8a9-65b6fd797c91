<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yyzs</groupId>
        <artifactId>modules</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>yyzs-monitor</artifactId>
    <packaging>jar</packaging>

    <name>YY-ZS Monitor Module</name>
    <description>System monitoring module including performance metrics, logs, alerts</description>

    <dependencies>
        <!-- Common Module (包含所有Spring Boot依赖) -->
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yyzs-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Spring Boot Starter Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Micrometer Prometheus -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
