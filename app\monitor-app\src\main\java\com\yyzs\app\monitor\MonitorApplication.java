package com.yyzs.app.monitor;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 监控应用启动类
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {
    "com.yyzs.common",
    "com.yyzs.modules.monitor",
    "com.yyzs.modules.notification",
    "com.yyzs.plugins.logging",
    "com.yyzs.plugins.integration",
    "com.yyzs.app.monitor"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.monitor.mapper",
    "com.yyzs.modules.notification.mapper"
})
public class MonitorApplication {

    public static void main(String[] args) {
        SpringApplication.run(MonitorApplication.class, args);
    }
}
