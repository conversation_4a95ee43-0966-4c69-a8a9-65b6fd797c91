package com.yyzs.common.aspect;

import com.yyzs.common.annotation.DataSource;
import com.yyzs.common.context.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据源切换切面
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Aspect
@Component
@Order(1) // 确保在事务切面之前执行
@Slf4j
public class DataSourceAspect {

    @Pointcut("@annotation(com.yyzs.common.annotation.DataSource) || @within(com.yyzs.common.annotation.DataSource)")
    public void dataSourcePointCut() {
    }

    @Around("dataSourcePointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        DataSource dataSource = getDataSource(point);
        
        if (dataSource != null) {
            DataSource.DataSourceType dataSourceType = dataSource.value();
            DataSourceContextHolder.setDataSourceType(dataSourceType);
            log.debug("Switch to datasource: {}", dataSourceType);
        }
        
        try {
            return point.proceed();
        } finally {
            DataSourceContextHolder.clearDataSourceType();
            log.debug("Clear datasource context");
        }
    }

    /**
     * 获取需要切换的数据源
     */
    public DataSource getDataSource(ProceedingJoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        
        // 先查找方法上的注解
        DataSource dataSource = AnnotationUtils.findAnnotation(method, DataSource.class);
        if (dataSource != null) {
            return dataSource;
        }
        
        // 再查找类上的注解
        Class<?> targetClass = point.getTarget().getClass();
        return AnnotationUtils.findAnnotation(targetClass, DataSource.class);
    }
}
