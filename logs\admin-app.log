2025-08-31 15:17:21 [main] INFO  com.yyzs.app.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 15056 (E:\Workspace\yy-zs\app\yyzs-admin\target\classes started by chenx in E:\Workspace\yy-zs)
2025-08-31 15:17:21 [main] DEBUG com.yyzs.app.admin.AdminApplication - Running with Spring Boot v3.5.5, Spring v6.2.10
2025-08-31 15:17:21 [main] INFO  com.yyzs.app.admin.AdminApplication - The following 2 profiles are active: "common", "dev"
2025-08-31 15:17:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:17:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 15:17:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 JPA repository interfaces.
2025-08-31 15:17:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:17:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-31 15:17:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-08-31 15:17:22 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.app.admin]' package. Please check your configuration.
2025-08-31 15:17:23 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.file.mapper]' package. Please check your configuration.
2025-08-31 15:17:23 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.monitor.mapper]' package. Please check your configuration.
2025-08-31 15:17:23 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.notification.mapper]' package. Please check your configuration.
2025-08-31 15:17:23 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'roleMapper' and 'com.yyzs.modules.system.mapper.RoleMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:17:23 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yyzs.modules.system.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:17:23 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.system.mapper]' package. Please check your configuration.
2025-08-31 15:17:23 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-08-31 15:17:23 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 15:17:23 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:864)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:665)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:651)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:611)
	at org.springframework.beans.factory.BeanFactoryUtils.beanNamesForTypeIncludingAncestors(BeanFactoryUtils.java:264)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.getBeanNamesForType(OAuth2ClientConfiguration.java:426)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.postProcessBeanDefinitionRegistry(OAuth2ClientConfiguration.java:190)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:148)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.yyzs.app.admin.AdminApplication.main(AdminApplication.java:20)
2025-08-31 15:19:02 [main] INFO  com.yyzs.app.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 11116 (E:\Workspace\yy-zs\app\yyzs-admin\target\classes started by chenx in E:\Workspace\yy-zs)
2025-08-31 15:19:02 [main] DEBUG com.yyzs.app.admin.AdminApplication - Running with Spring Boot v3.5.5, Spring v6.2.10
2025-08-31 15:19:02 [main] INFO  com.yyzs.app.admin.AdminApplication - The following 2 profiles are active: "common", "dev"
2025-08-31 15:19:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:19:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 15:19:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 JPA repository interfaces.
2025-08-31 15:19:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:19:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-31 15:19:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-08-31 15:19:04 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.app.admin]' package. Please check your configuration.
2025-08-31 15:19:04 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.file.mapper]' package. Please check your configuration.
2025-08-31 15:19:04 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.monitor.mapper]' package. Please check your configuration.
2025-08-31 15:19:04 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.notification.mapper]' package. Please check your configuration.
2025-08-31 15:19:04 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'roleMapper' and 'com.yyzs.modules.system.mapper.RoleMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:19:04 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yyzs.modules.system.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:19:04 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.system.mapper]' package. Please check your configuration.
2025-08-31 15:19:04 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-08-31 15:19:04 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 15:19:04 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:864)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:665)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:651)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:611)
	at org.springframework.beans.factory.BeanFactoryUtils.beanNamesForTypeIncludingAncestors(BeanFactoryUtils.java:264)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.getBeanNamesForType(OAuth2ClientConfiguration.java:426)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.postProcessBeanDefinitionRegistry(OAuth2ClientConfiguration.java:190)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:148)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.yyzs.app.admin.AdminApplication.main(AdminApplication.java:20)
2025-08-31 15:25:48 [main] INFO  com.yyzs.app.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 1844 (E:\Workspace\yy-zs\app\yyzs-admin\target\classes started by chenx in E:\Workspace\yy-zs)
2025-08-31 15:25:48 [main] DEBUG com.yyzs.app.admin.AdminApplication - Running with Spring Boot v3.5.5, Spring v6.2.10
2025-08-31 15:25:48 [main] INFO  com.yyzs.app.admin.AdminApplication - The following 2 profiles are active: "common", "dev"
2025-08-31 15:25:49 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:25:49 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 15:25:49 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 JPA repository interfaces.
2025-08-31 15:25:49 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:25:49 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-31 15:25:49 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-08-31 15:25:49 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.app.admin]' package. Please check your configuration.
2025-08-31 15:25:49 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.file.mapper]' package. Please check your configuration.
2025-08-31 15:25:49 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.monitor.mapper]' package. Please check your configuration.
2025-08-31 15:25:49 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.notification.mapper]' package. Please check your configuration.
2025-08-31 15:25:49 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'roleMapper' and 'com.yyzs.modules.system.mapper.RoleMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:25:49 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yyzs.modules.system.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:25:49 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.system.mapper]' package. Please check your configuration.
2025-08-31 15:25:49 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-08-31 15:25:49 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 15:25:49 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:864)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:665)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:651)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:611)
	at org.springframework.beans.factory.BeanFactoryUtils.beanNamesForTypeIncludingAncestors(BeanFactoryUtils.java:264)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.getBeanNamesForType(OAuth2ClientConfiguration.java:426)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.postProcessBeanDefinitionRegistry(OAuth2ClientConfiguration.java:190)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:148)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.yyzs.app.admin.AdminApplication.main(AdminApplication.java:20)
2025-08-31 15:45:01 [main] INFO  com.yyzs.app.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 19076 (E:\Workspace\yy-zs\app\yyzs-admin\target\classes started by chenx in E:\Workspace\yy-zs)
2025-08-31 15:45:01 [main] DEBUG com.yyzs.app.admin.AdminApplication - Running with Spring Boot v3.5.5, Spring v6.2.10
2025-08-31 15:45:01 [main] INFO  com.yyzs.app.admin.AdminApplication - The following 2 profiles are active: "common", "dev"
2025-08-31 15:45:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:45:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 15:45:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 JPA repository interfaces.
2025-08-31 15:45:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-31 15:45:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-31 15:45:02 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-08-31 15:45:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.app.admin]' package. Please check your configuration.
2025-08-31 15:45:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.file.mapper]' package. Please check your configuration.
2025-08-31 15:45:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.monitor.mapper]' package. Please check your configuration.
2025-08-31 15:45:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.notification.mapper]' package. Please check your configuration.
2025-08-31 15:45:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'roleMapper' and 'com.yyzs.modules.system.mapper.RoleMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:45:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yyzs.modules.system.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-08-31 15:45:02 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yyzs.modules.system.mapper]' package. Please check your configuration.
2025-08-31 15:45:02 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-08-31 15:45:02 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 15:45:02 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'roleMapper' defined in file [E:\Workspace\yy-zs\modules\yyzs-system\target\classes\com\yyzs\modules\system\mapper\RoleMapper.class]: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:864)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:665)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:651)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:611)
	at org.springframework.beans.factory.BeanFactoryUtils.beanNamesForTypeIncludingAncestors(BeanFactoryUtils.java:264)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.getBeanNamesForType(OAuth2ClientConfiguration.java:426)
	at org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2AuthorizedClientManagerRegistrar.postProcessBeanDefinitionRegistry(OAuth2ClientConfiguration.java:190)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:148)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.yyzs.app.admin.AdminApplication.main(AdminApplication.java:20)
