<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/app/agent-app/pom.xml" />
        <option value="$PROJECT_DIR$/app/monitor-app/pom.xml" />
        <option value="$PROJECT_DIR$/plugins/cache-plugin/pom.xml" />
        <option value="$PROJECT_DIR$/plugins/integration-plugin/pom.xml" />
        <option value="$PROJECT_DIR$/plugins/logging-plugin/pom.xml" />
        <option value="$PROJECT_DIR$/plugins/security-plugin/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21_PREVIEW" project-jdk-name="21" project-jdk-type="JavaSDK" />
</project>