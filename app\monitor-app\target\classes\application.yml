# Monitor Application Configuration
spring:
  application:
    name: yy-zs-monitor-app
  
  profiles:
    active: dev
    include: common

# Server Configuration
server:
  port: 8083
  servlet:
    context-path: /api/monitor

# Application specific configuration
yyzs:
  app:
    name: "YY-ZS Monitor Application"
    description: "System monitoring, metrics and alerts"
    version: "1.0.0"
  
  # Monitor specific configuration
  monitor:
    enabled: true
    collect-interval: 30000 # 30 seconds
    alert-interval: 60000 # 1 minute
    retention-days: 30
    
  # Alert configuration
  alert:
    enabled: true
    email-enabled: true
    sms-enabled: false
    webhook-enabled: true
    
  # Metrics configuration
  metrics:
    enabled: true
    export-interval: 60000 # 1 minute
    batch-size: 1000
    
  # Security configuration for monitor
  security:
    enabled: true
    ignore-urls:
      - /api/monitor/public/**
      - /actuator/**

# Prometheus metrics
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /api/monitor/actuator
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Logging configuration for monitor
logging:
  level:
    com.yyzs.app.monitor: debug
    com.yyzs.modules.monitor: debug
  file:
    name: logs/monitor-app.log

# WebSocket configuration for real-time monitoring
yyzs:
  websocket:
    enabled: true
    endpoint: "/api/monitor/ws"
    allowed-origins: "*"
