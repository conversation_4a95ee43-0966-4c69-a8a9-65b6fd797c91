<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yyzs</groupId>
        <artifactId>app</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>yyzs-admin</artifactId>
    <packaging>jar</packaging>

    <name>YY-ZS Admin Application</name>
    <description>Admin management application combining system, monitor, file modules</description>

    <dependencies>
        <!-- Modules -->
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yyzs-system</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yyzs-monitor</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yyzs-file</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yyzs-notification</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Plugins -->
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yy-zs-security-plugin</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yy-zs-logging-plugin</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
