package com.yyzs.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.yyzs.common.handler.YYTenantLineHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus配置类
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {
    "com.yyzs.modules.system.mapper"
    // 逐步添加其他模块，确保每个都能正常工作
     "com.yyzs.agent.mapper",
     "com.yyzs.modules.monitor.mapper",
     "com.yyzs.modules.file.mapper",
     "com.yyzs.modules.notification.mapper",
     "com.yyzs.modules.workflow.mapper"
})
public class MybatisPlusConfig {

    @Autowired
    private YYTenantLineHandler YYTenantLineHandler;

    /**
     * MyBatis-Plus拦截器配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 多租户插件 - 需要放在分页插件之前
        // 注意：如果YYTenantLineHandler为null或多租户功能禁用，则不添加多租户插件
        if (YYTenantLineHandler != null) {
            TenantLineInnerInterceptor tenantLineInnerInterceptor = new TenantLineInnerInterceptor();
            tenantLineInnerInterceptor.setTenantLineHandler(YYTenantLineHandler);
            interceptor.addInnerInterceptor(tenantLineInnerInterceptor);
        }

        // 分页插件
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        paginationInnerInterceptor.setDbType(DbType.POSTGRE_SQL);
        paginationInnerInterceptor.setOverflow(false);
        paginationInnerInterceptor.setMaxLimit(1000L);
        interceptor.addInnerInterceptor(paginationInnerInterceptor);

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // 防止全表更新与删除插件
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());


        return interceptor;
    }
}
