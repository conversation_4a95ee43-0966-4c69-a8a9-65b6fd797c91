<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-modules</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>yy-zs-notification-module</artifactId>
    <packaging>jar</packaging>

    <name>YY-ZS Notification Module</name>
    <description>Notification module including email, SMS, push notifications</description>

    <dependencies>
        <!-- Common Module (包含所有Spring Boot依赖) -->
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yy-zs-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Spring Boot Starter Mail -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- Spring Boot Starter WebSocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
