package com.yyzs.modules.file.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * File模块自动配置类
 *
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ComponentScan(
    basePackages = "com.yyzs.modules.file",
    excludeFilters = @ComponentScan.Filter(
        type = FilterType.REGEX,
        pattern = ".*AutoConfiguration"
    )
)
@MapperScan(basePackages = {
    "com.yyzs.modules.file.mapper"
})
@EntityScan(basePackages = {
    "com.yyzs.modules.file.entity"
})
public class FileModuleAutoConfiguration {
    
    // 可以在这里定义文件模块特定的Bean配置
    
}
