# Test Environment Configuration
spring:
  # Test DataSource Configuration (使用内存数据库)
  datasource:
    primary:
      url: jdbc:h2:mem:testdb_primary;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      driver-class-name: org.h2.Driver
      username: sa
      password: 
    secondary:
      url: jdbc:h2:mem:testdb_secondary;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      driver-class-name: org.h2.Driver
      username: sa
      password: 
    agent:
      url: jdbc:h2:mem:testdb_agent;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      driver-class-name: org.h2.Driver
      username: sa
      password: 

  # Test JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    database-platform: org.hibernate.dialect.H2Dialect

  # Test Redis Configuration (使用嵌入式Redis)
  data:
    redis:
      host: localhost
      port: 6370 # 不同端口避免冲突
      database: 15 # 使用最后一个数据库

  # H2 Console (for testing)
  h2:
    console:
      enabled: true
      path: /h2-console

# Test Logging Configuration
logging:
  level:
    root: warn
    com.yyzs: debug
    org.springframework.test: debug
    org.hibernate.SQL: debug
  pattern:
    console: '%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'

# Test Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# Test YY-ZS Configuration
yyzs:
  # Test mode
  debug: true

  # Test CORS (disabled for testing)
  cors:
    enabled: false

  # Test file upload
  upload:
    path: ./test-uploads/

  # Test security (disabled)
  security:
    enabled: false
