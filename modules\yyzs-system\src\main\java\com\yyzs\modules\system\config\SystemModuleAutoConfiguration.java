package com.yyzs.modules.system.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * System模块自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ComponentScan(basePackages = {
    "com.yyzs.modules.system.*"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.system.mapper"
})
@EntityScan(basePackages = {
    "com.yyzs.modules.system.entity"
})
public class SystemModuleAutoConfiguration {
    
    // 可以在这里定义模块特定的Bean配置
    
}
