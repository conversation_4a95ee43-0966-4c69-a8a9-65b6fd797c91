package com.yyzs.modules.system.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * System模块自动配置类
 *
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.modules.system.entity.User")
@ComponentScan(basePackages = {
    "com.yyzs.modules.system.service",
    "com.yyzs.modules.system.controller",
    "com.yyzs.modules.system.entity"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.system.mapper"
})
@EntityScan(basePackages = {
    "com.yyzs.modules.system.entity"
})
public class SystemModuleAutoConfiguration {
    
    // 可以在这里定义模块特定的Bean配置
    
}
