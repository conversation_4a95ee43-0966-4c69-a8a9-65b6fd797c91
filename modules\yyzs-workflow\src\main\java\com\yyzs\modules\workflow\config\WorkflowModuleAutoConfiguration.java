package com.yyzs.modules.workflow.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * Workflow模块自动配置类
 *
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ComponentScan(
    basePackages = "com.yyzs.modules.workflow",
    excludeFilters = @ComponentScan.Filter(
        type = FilterType.REGEX,
        pattern = ".*AutoConfiguration"
    )
)
@MapperScan(basePackages = {
    "com.yyzs.modules.workflow.mapper"
})
@EntityScan(basePackages = {
    "com.yyzs.modules.workflow.entity"
})
public class WorkflowModuleAutoConfiguration {
    
    // 可以在这里定义工作流模块特定的Bean配置
    
}
