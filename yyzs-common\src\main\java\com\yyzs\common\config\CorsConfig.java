package com.yyzs.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

/**
 * 跨域配置类
 * 统一管理所有应用的跨域配置
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Configuration
@ConfigurationProperties(prefix = "yyzs.cors")
@Data
public class CorsConfig implements WebMvcConfigurer {

    /**
     * 是否启用跨域
     */
    private boolean enabled = true;

    /**
     * 允许的源
     */
    private List<String> allowedOrigins = Arrays.asList(
        "http://localhost:3000",
        "http://localhost:8080",
        "http://localhost:8081",
        "http://localhost:8082",
        "http://localhost:8083",
        "https://admin.yyzs.com",
        "https://api.yyzs.com"
    );

    /**
     * 允许的方法
     */
    private List<String> allowedMethods = Arrays.asList(
        "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"
    );

    /**
     * 允许的头部
     */
    private List<String> allowedHeaders = Arrays.asList(
        "*"
    );

    /**
     * 暴露的头部
     */
    private List<String> exposedHeaders = Arrays.asList(
        "Authorization",
        "Content-Type",
        "X-Total-Count",
        "X-Request-Id"
    );

    /**
     * 是否允许凭证
     */
    private boolean allowCredentials = true;

    /**
     * 预检请求缓存时间（秒）
     */
    private long maxAge = 3600;

    /**
     * 路径模式
     */
    private String pathPattern = "/api/**";

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        if (!enabled) {
            return;
        }

        registry.addMapping(pathPattern)
                .allowedOriginPatterns(allowedOrigins.toArray(new String[0]))
                .allowedMethods(allowedMethods.toArray(new String[0]))
                .allowedHeaders(allowedHeaders.toArray(new String[0]))
                .exposedHeaders(exposedHeaders.toArray(new String[0]))
                .allowCredentials(allowCredentials)
                .maxAge(maxAge);
    }

    /**
     * 配置CORS配置源（用于Spring Security）
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        if (!enabled) {
            return new UrlBasedCorsConfigurationSource();
        }

        // 设置允许的源
        configuration.setAllowedOriginPatterns(allowedOrigins);
        
        // 设置允许的方法
        configuration.setAllowedMethods(allowedMethods);
        
        // 设置允许的头部
        configuration.setAllowedHeaders(allowedHeaders);
        
        // 设置暴露的头部
        configuration.setExposedHeaders(exposedHeaders);
        
        // 是否允许凭证
        configuration.setAllowCredentials(allowCredentials);
        
        // 预检请求缓存时间
        configuration.setMaxAge(maxAge);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration(pathPattern, configuration);
        
        return source;
    }
}
