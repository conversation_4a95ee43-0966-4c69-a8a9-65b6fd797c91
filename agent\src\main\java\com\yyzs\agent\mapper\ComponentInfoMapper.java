package com.yyzs.agent.mapper;

import com.yyzs.agent.entity.ComponentInfo;
import com.yyzs.common.annotation.DataSource;
import com.yyzs.common.mapper.YYBaseMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 组件信息Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Repository
@Mapper
public interface ComponentInfoMapper extends YYBaseMapper<ComponentInfo> {

    /**
     * 根据Agent ID查询组件列表
     */
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM component_info WHERE agent_id = #{agentId} AND deleted = 0 ORDER BY create_time DESC")
    List<ComponentInfo> findByAgentId(@Param("agentId") String agentId);

    /**
     * 根据组件名称和Agent ID查询组件
     */
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM component_info WHERE component_name = #{componentName} AND agent_id = #{agentId} AND deleted = 0")
    ComponentInfo findByNameAndAgentId(@Param("componentName") String componentName, @Param("agentId") String agentId);

    /**
     * 根据组件类型查询组件列表
     */
    @Select("SELECT * FROM component_info WHERE component_type = #{componentType} AND deleted = 0 ORDER BY create_time DESC")
    List<ComponentInfo> findByComponentType(@Param("componentType") String componentType);

    /**
     * 查询运行中的组件列表
     */
    @Select("SELECT * FROM component_info WHERE status = 2 AND deleted = 0 ORDER BY start_time DESC")
    List<ComponentInfo> findRunningComponents();

    /**
     * 根据Agent ID查询运行中的组件
     */
    @Select("SELECT * FROM component_info WHERE agent_id = #{agentId} AND status = 2 AND deleted = 0")
    List<ComponentInfo> findRunningComponentsByAgentId(@Param("agentId") String agentId);

    /**
     * 更新组件状态
     */
    @Update("UPDATE component_info SET status = #{status}, last_check_time = #{lastCheckTime}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("lastCheckTime") LocalDateTime lastCheckTime);

    /**
     * 更新组件进程ID
     */
    @Update("UPDATE component_info SET process_id = #{processId}, start_time = #{startTime}, status = 2, update_time = NOW() WHERE id = #{id}")
    int updateProcessId(@Param("id") Long id, @Param("processId") Integer processId, @Param("startTime") LocalDateTime startTime);

    /**
     * 清除组件进程ID
     */
    @Update("UPDATE component_info SET process_id = NULL, status = 3, update_time = NOW() WHERE id = #{id}")
    int clearProcessId(@Param("id") Long id);

    /**
     * 根据端口查询组件
     */
    @Select("SELECT * FROM component_info WHERE port = #{port} AND agent_id = #{agentId} AND deleted = 0")
    List<ComponentInfo> findByPort(@Param("port") Integer port, @Param("agentId") String agentId);

    /**
     * 批量更新组件状态
     */
    @Update("<script>" +
            "UPDATE component_info SET status = #{status}, last_check_time = #{lastCheckTime}, update_time = NOW() " +
            "WHERE agent_id = #{agentId} AND id IN " +
            "<foreach collection='componentIds' item='id' open='(' separator=',' close=')'>" +
            "  #{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("agentId") String agentId, 
                         @Param("componentIds") List<Long> componentIds, 
                         @Param("status") Integer status, 
                         @Param("lastCheckTime") LocalDateTime lastCheckTime);

    /**
     * 根据条件查询组件数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM component_info WHERE deleted = 0 " +
            "<if test='agentId != null and agentId != \"\"'>" +
            "  AND agent_id = #{agentId}" +
            "</if>" +
            "<if test='componentType != null and componentType != \"\"'>" +
            "  AND component_type = #{componentType}" +
            "</if>" +
            "<if test='status != null'>" +
            "  AND status = #{status}" +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  AND component_name LIKE CONCAT('%', #{keyword}, '%')" +
            "</if>" +
            "</script>")
    Long countByCondition(@Param("agentId") String agentId,
                         @Param("componentType") String componentType,
                         @Param("status") Integer status,
                         @Param("keyword") String keyword);

    /**
     * 分页查询组件列表
     */
    @Select("<script>" +
            "SELECT * FROM component_info WHERE deleted = 0 " +
            "<if test='agentId != null and agentId != \"\"'>" +
            "  AND agent_id = #{agentId}" +
            "</if>" +
            "<if test='componentType != null and componentType != \"\"'>" +
            "  AND component_type = #{componentType}" +
            "</if>" +
            "<if test='status != null'>" +
            "  AND status = #{status}" +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  AND component_name LIKE CONCAT('%', #{keyword}, '%')" +
            "</if>" +
            "ORDER BY create_time DESC " +
            "LIMIT #{offset}, #{limit}" +
            "</script>")
    List<ComponentInfo> findByPage(@Param("agentId") String agentId,
                                  @Param("componentType") String componentType,
                                  @Param("status") Integer status,
                                  @Param("keyword") String keyword,
                                  @Param("offset") Long offset,
                                  @Param("limit") Long limit);

    /**
     * 统计组件状态分布
     */
    @Select("SELECT status, COUNT(*) as count FROM component_info WHERE deleted = 0 GROUP BY status")
    @Results({
        @Result(column = "status", property = "status"),
        @Result(column = "count", property = "count")
    })
    List<ComponentStatusCount> countByStatus();

    /**
     * 根据Agent ID统计组件状态分布
     */
    @Select("SELECT status, COUNT(*) as count FROM component_info WHERE agent_id = #{agentId} AND deleted = 0 GROUP BY status")
    @Results({
        @Result(column = "status", property = "status"),
        @Result(column = "count", property = "count")
    })
    List<ComponentStatusCount> countByStatusAndAgentId(@Param("agentId") String agentId);

    /**
     * 组件状态统计内部类
     */
    class ComponentStatusCount {
        private Integer status;
        private Long count;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }
    }
}
