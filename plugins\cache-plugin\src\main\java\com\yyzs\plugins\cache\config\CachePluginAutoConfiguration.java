package com.yyzs.plugins.cache.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;

/**
 * Cache插件自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.plugins.cache.service.CacheService")
@ComponentScan(basePackages = {
    "com.yyzs.plugins.cache.service",
    "com.yyzs.plugins.cache.config",
    "com.yyzs.plugins.cache.aspect"
})
public class CachePluginAutoConfiguration {
    
    // 可以在这里定义缓存插件特定的Bean配置
    
}
