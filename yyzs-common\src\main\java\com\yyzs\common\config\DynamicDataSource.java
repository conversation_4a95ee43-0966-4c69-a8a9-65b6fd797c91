package com.yyzs.common.config;

import com.yyzs.common.annotation.DataSource;
import com.yyzs.common.context.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * 动态数据源路由器
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Slf4j
public class DynamicDataSource extends AbstractRoutingDataSource {

    @Override
    protected Object determineCurrentLookupKey() {
        DataSource.DataSourceType dataSourceType = DataSourceContextHolder.getDataSourceType();
        if (dataSourceType == null) {
            dataSourceType = DataSource.DataSourceType.PRIMARY;
        }
        log.debug("Current datasource: {}", dataSourceType);
        return dataSourceType;
    }
}
