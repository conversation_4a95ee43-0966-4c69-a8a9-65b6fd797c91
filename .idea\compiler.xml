<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="yyzs-admin" />
        <module name="yyzs-system" />
        <module name="yyzs-agent" />
        <module name="yyzs-notification" />
        <module name="yyzs-workflow" />
        <module name="yyzs-monitor" />
        <module name="yyzs-common" />
        <module name="yyzs-file" />
        <module name="yyzs-database" />
        <module name="yyzs-api" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="yy-zs-agent-app" target="17" />
      <module name="yy-zs-integration-plugin" target="17" />
      <module name="yy-zs-logging-plugin" target="17" />
      <module name="yy-zs-monitor-app" target="17" />
      <module name="yy-zs-security-plugin" target="17" />
      <module name="yyzs-cache" target="17" />
    </bytecodeTargetLevel>
  </component>
</project>