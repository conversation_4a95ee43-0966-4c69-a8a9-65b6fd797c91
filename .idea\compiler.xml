<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="yy-zs-system-module" />
        <module name="yy-zs-api-app" />
        <module name="yy-zs-cache-plugin" />
        <module name="yy-zs-common" />
        <module name="yy-zs-notification-module" />
        <module name="yy-zs-database-plugin" />
        <module name="yy-zs-admin-app" />
        <module name="yy-zs-monitor-app" />
        <module name="yy-zs-monitor-module" />
        <module name="yy-zs-security-plugin" />
        <module name="yy-zs-integration-plugin" />
        <module name="yy-zs-agent-app" />
        <module name="yy-zs-file-module" />
        <module name="yy-zs-agent" />
        <module name="yy-zs-workflow-module" />
        <module name="yy-zs-logging-plugin" />
      </profile>
    </annotationProcessing>
  </component>
</project>