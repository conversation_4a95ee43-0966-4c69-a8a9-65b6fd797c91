# Mapper扫描重构说明

## 问题背景

之前在 `common/src/main/java/com/yyzs/common/config/MybatisPlusConfig.java` 中统一配置了 `@MapperScan` 注解，扫描所有模块的 Mapper 接口。由于 common 模块被所有应用引用，这导致了以下问题：

1. **重复扫描**：每个应用都会扫描所有模块的 Mapper，即使不需要
2. **不灵活**：无法根据应用需求选择性扫描 Mapper
3. **性能问题**：不必要的 Bean 创建和内存占用
4. **启动错误**：重复扫描导致 Bean 定义冲突

## 解决方案

### 1. 移除统一配置

从 `MybatisPlusConfig.java` 中移除 `@MapperScan` 注解，只保留 MyBatis-Plus 插件配置。

### 2. 分散到各应用

在每个应用的启动类中添加 `@MapperScan` 注解，根据应用需求扫描相应的 Mapper 包。

## 重构后的配置

### Admin Application (管理后台)
```java
@MapperScan(basePackages = {
    "com.yyzs.modules.system.mapper",
    "com.yyzs.modules.monitor.mapper", 
    "com.yyzs.modules.file.mapper",
    "com.yyzs.modules.notification.mapper",
    "com.yyzs.modules.workflow.mapper"
})
```

### Agent Application (Agent应用)
```java
@MapperScan(basePackages = {
    "com.yyzs.agent.mapper",
    "com.yyzs.modules.monitor.mapper"
})
```

### API Application (API应用)
```java
@MapperScan(basePackages = {
    "com.yyzs.modules.system.mapper",
    "com.yyzs.modules.file.mapper",
    "com.yyzs.modules.workflow.mapper",
    "com.yyzs.agent.mapper"
})
```

### Monitor Application (监控应用)
```java
@MapperScan(basePackages = {
    "com.yyzs.modules.monitor.mapper",
    "com.yyzs.modules.notification.mapper"
})
```

## 优势

### 1. 避免重复扫描
- 每个应用只扫描自己需要的 Mapper
- 消除了 Bean 定义冲突的问题

### 2. 提高灵活性
- 可以根据应用功能选择性扫描
- 便于后续模块的添加和移除

### 3. 性能优化
- 减少不必要的 Bean 创建
- 降低内存占用
- 加快应用启动速度

### 4. 清晰的依赖关系
- 从启动类就能看出应用依赖哪些模块
- 便于理解和维护

## 注意事项

### 1. 新增模块时
当添加新的模块时，需要在相应的应用启动类中添加对应的 Mapper 扫描路径。

### 2. 模块重命名时
如果模块包名发生变化，需要同步更新所有相关应用的 `@MapperScan` 配置。

### 3. 测试验证
重构后需要分别测试每个应用，确保所需的 Mapper 都能正常注入。

## 验证步骤

1. **清理编译缓存**
   ```bash
   mvn clean
   ```

2. **重新编译**
   ```bash
   mvn compile
   ```

3. **分别启动各应用**
   ```bash
   # 启动 Admin 应用
   cd app/admin-app
   mvn spring-boot:run
   
   # 启动 Agent 应用  
   cd app/agent-app
   mvn spring-boot:run
   
   # 启动 API 应用
   cd app/api-app
   mvn spring-boot:run
   
   # 启动 Monitor 应用
   cd app/monitor-app
   mvn spring-boot:run
   ```

4. **检查日志**
   确保没有 "Bean already defined" 或 "Invalid bean definition" 等错误信息。

## 总结

这次重构解决了 Mapper 重复扫描的问题，提高了系统的灵活性和性能。每个应用现在只加载自己需要的 Mapper，避免了不必要的资源浪费和潜在的冲突问题。
