package com.yyzs.modules.system.service;

import com.yyzs.common.annotation.DataSource;
import com.yyzs.modules.system.entity.Role;
import com.yyzs.modules.system.mapper.RoleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 角色服务类
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleService {

    private final RoleMapper roleMapper;

    /**
     * 创建角色
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(Role role) {
        log.info("Creating role: {}", role.getRoleName());
        
        // 检查角色编码是否已存在
        if (roleMapper.checkRoleCodeExists(role.getRoleCode()) > 0) {
            throw new RuntimeException("角色编码已存在");
        }
        
        return roleMapper.insert(role) > 0;
    }

    /**
     * 更新角色
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(Role role) {
        log.info("Updating role: {}", role.getId());
        
        // 检查角色编码是否已存在（排除当前角色）
        if (roleMapper.checkRoleCodeExistsExcludeId(role.getRoleCode(), role.getId()) > 0) {
            throw new RuntimeException("角色编码已存在");
        }
        
        return roleMapper.updateById(role) > 0;
    }

    /**
     * 删除角色
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(String id) {
        log.info("Deleting role: {}", id);
        return roleMapper.deleteById(id) > 0;
    }

    /**
     * 批量删除角色
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteRoles(List<String> ids) {
        log.info("Batch deleting roles: {}", ids);
        return roleMapper.batchDelete(ids) > 0;
    }

    /**
     * 根据ID查询角色
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public Role getRoleById(String id) {
        log.info("Getting role by id: {}", id);
        return roleMapper.selectById(id);
    }

    /**
     * 根据角色编码查询角色
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public Role getRoleByCode(String roleCode) {
        log.info("Getting role by code: {}", roleCode);
        return roleMapper.findByRoleCode(roleCode);
    }

    /**
     * 查询启用的角色列表
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public List<Role> getEnabledRoles() {
        log.info("Getting enabled roles");
        return roleMapper.findEnabledRoles();
    }

    /**
     * 根据用户ID查询角色列表
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public List<Role> getRolesByUserId(String userId) {
        log.info("Getting roles by user id: {}", userId);
        return roleMapper.findByUserId(userId);
    }

    /**
     * 更新角色状态
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleStatus(String id, Integer status) {
        log.info("Updating role status: id={}, status={}", id, status);
        return roleMapper.updateStatus(id, status) > 0;
    }

    /**
     * 分页查询角色列表
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public List<Role> getRolesByPage(Integer status, String keyword, Long offset, Long limit) {
        log.info("Getting roles by page: status={}, keyword={}, offset={}, limit={}", 
                status, keyword, offset, limit);
        return roleMapper.findByPage(status, keyword, offset, limit);
    }

    /**
     * 统计角色数量
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public Long countRoles(Integer status, String keyword) {
        log.info("Counting roles: status={}, keyword={}", status, keyword);
        return roleMapper.countByCondition(status, keyword);
    }

    /**
     * 检查角色编码是否存在
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public boolean checkRoleCodeExists(String roleCode) {
        return roleMapper.checkRoleCodeExists(roleCode) > 0;
    }

    /**
     * 检查角色编码是否存在（排除指定ID）
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public boolean checkRoleCodeExists(String roleCode, String excludeId) {
        return roleMapper.checkRoleCodeExistsExcludeId(roleCode, excludeId) > 0;
    }
}
