# Mapper Bean定义问题解决方案

## 问题描述

遇到以下错误：
```
Invalid bean definition with name 'roleMapper' defined in file: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
```

## 问题原因

1. **MapperScan重复扫描**：多个地方配置了MapperScan，导致同一个Mapper被扫描多次
2. **类级别DataSource注解冲突**：在Mapper接口上使用类级别的@DataSource注解可能导致Bean定义问题
3. **复杂的BaseMapper定义**：过于复杂的BaseMapper注解可能导致泛型解析问题
4. **重复的Mapper文件**：存在重复的Mapper接口定义

## 解决方案

### 1. 优化MapperScan配置

**修改前（问题配置）：**
```java
@MapperScan(basePackages = {
    "com.yyzs.**.mapper",  // 过于宽泛，可能重复扫描
    "com.yyzs.agent.mapper",
    "com.yyzs.modules.**.mapper",
    "com.yyzs.plugins.**.mapper"
})
```

**修改后（正确配置）：**
```java
@MapperScan(basePackages = {
    "com.yyzs.common.mapper",
    "com.yyzs.agent.mapper",
    "com.yyzs.modules.system.mapper",
    "com.yyzs.modules.monitor.mapper",
    "com.yyzs.modules.file.mapper",
    "com.yyzs.modules.notification.mapper",
    "com.yyzs.modules.workflow.mapper"
})
```

### 2. 移除类级别DataSource注解

**修改前（问题配置）：**
```java
@Repository
@Mapper
@DataSource(DataSource.DataSourceType.PRIMARY)  // 类级别注解可能导致问题
public interface RoleMapper extends YYBaseMapper<Role> {
    // ...
}
```

**修改后（正确配置）：**
```java
@Repository
@Mapper
public interface RoleMapper extends YYBaseMapper<Role> {
    
    @DataSource(DataSource.DataSourceType.SECONDARY)  // 方法级别注解
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode}")
    Role findByRoleCode(@Param("roleCode") String roleCode);
    
    @DataSource(DataSource.DataSourceType.PRIMARY)   // 写操作使用主数据源
    @Update("UPDATE sys_role SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") Integer status);
}
```

### 3. 简化BaseMapper定义

**修改前（复杂配置）：**
```java
public interface YYBaseMapper<T> extends BaseMapper<T> {
    // 包含复杂的注解方法定义
    @Select("SELECT * FROM ${tableName} WHERE id = #{id}")
    T selectByIdWithAnnotation(...);
    // 更多复杂方法...
}
```

**修改后（简化配置）：**
```java
public interface YYBaseMapper<T> extends BaseMapper<T> {
    // 继承MyBatis-Plus的BaseMapper，提供基础的CRUD操作
    // 具体的业务方法在各个具体的Mapper接口中使用注解定义
}
```

### 4. 删除重复的Mapper文件

检查并删除重复的Mapper文件：
- `AgentInfoMapper` 和 `AgentInfoMapperYY`
- `ComponentInfoMapper` 和 `ComponentInfoMapperYY`

### 5. 数据源注解最佳实践

```java
@Repository
@Mapper
public interface UserMapper extends YYBaseMapper<User> {
    
    // 查询操作使用从数据源
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT * FROM sys_user WHERE username = #{username}")
    User findByUsername(@Param("username") String username);
    
    // 写操作使用主数据源
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Insert("INSERT INTO sys_user(username, email) VALUES(#{username}, #{email})")
    int insertUser(@Param("username") String username, @Param("email") String email);
    
    // Agent相关操作使用Agent数据源
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM agent_user WHERE agent_id = #{agentId}")
    List<User> findByAgentId(@Param("agentId") String agentId);
}
```

## 验证步骤

### 1. 清理编译缓存
```bash
mvn clean
```

### 2. 检查重复定义
```bash
# 搜索重复的Mapper定义
find . -name "*Mapper*.java" | grep -E "(YY|duplicate)"
```

### 3. 验证MapperScan配置
确保只有一个地方配置了MapperScan，通常在MybatisPlusConfig中。

### 4. 测试启动
```bash
mvn spring-boot:run
```

## 常见问题

### Q1: 为什么不能在Mapper接口上使用类级别的@DataSource注解？
A: 类级别的@DataSource注解可能与MyBatis-Plus的Bean创建机制冲突，导致factoryBeanObjectType解析错误。建议使用方法级别的注解。

### Q2: MapperScan配置过于宽泛有什么问题？
A: 过于宽泛的包扫描可能导致：
- 重复扫描同一个Mapper
- 扫描到测试类或其他非Mapper接口
- 性能问题

### Q3: 如何确保数据源切换正确？
A: 
- 查询操作使用SECONDARY数据源
- 写操作使用PRIMARY数据源
- Agent相关操作使用AGENT数据源
- 在方法级别明确指定数据源

### Q4: BaseMapper应该如何设计？
A: 
- 保持简单，只继承MyBatis-Plus的BaseMapper
- 复杂的业务方法在具体的Mapper中定义
- 避免在BaseMapper中使用复杂的注解

## 预防措施

1. **统一配置管理**：所有MapperScan配置集中在一个地方
2. **命名规范**：避免创建重复名称的Mapper文件
3. **注解规范**：统一使用方法级别的@DataSource注解
4. **定期检查**：定期检查是否有重复的Bean定义

通过以上解决方案，可以有效避免Mapper Bean定义相关的问题。
