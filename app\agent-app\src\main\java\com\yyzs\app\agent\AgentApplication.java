package com.yyzs.app.agent;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Agent应用启动类
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {
    "com.yyzs.common",
    "com.yyzs.agent",
    "com.yyzs.modules.monitor",
    "com.yyzs.plugins.logging",
    "com.yyzs.plugins.integration",
    "com.yyzs.app.agent"
})
@MapperScan(basePackages = {
    "com.yyzs.agent.mapper",
    "com.yyzs.modules.monitor.mapper"
})
public class AgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(AgentApplication.class, args);
    }
}
