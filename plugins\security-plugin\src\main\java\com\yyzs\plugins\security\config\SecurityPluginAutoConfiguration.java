package com.yyzs.plugins.security.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;

/**
 * Security插件自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.plugins.security.service.SecurityService")
@ComponentScan(basePackages = {
    "com.yyzs.plugins.security.service",
    "com.yyzs.plugins.security.config",
    "com.yyzs.plugins.security.filter",
    "com.yyzs.plugins.security.handler"
})
public class SecurityPluginAutoConfiguration {
    
    // 可以在这里定义安全插件特定的Bean配置
    
}
