package com.yyzs.modules.monitor.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * Monitor模块自动配置类
 *
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.modules.monitor.entity.SystemMetrics")
@ComponentScan(basePackages = {
    "com.yyzs.modules.monitor.*"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.monitor.mapper"
})
@EntityScan(basePackages = {
    "com.yyzs.modules.monitor.entity"
})
public class MonitorModuleAutoConfiguration {
    
    // 可以在这里定义监控模块特定的Bean配置
    
}
