package com.yyzs.modules.monitor.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * Monitor模块自动配置类
 *
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ComponentScan(
    basePackages = "com.yyzs.modules.monitor",
    excludeFilters = @ComponentScan.Filter(
        type = FilterType.REGEX,
        pattern = ".*AutoConfiguration"
    )
)
@MapperScan(basePackages = {
    "com.yyzs.modules.monitor.mapper"
})
@EntityScan(basePackages = {
    "com.yyzs.modules.monitor.entity"
})
public class MonitorModuleAutoConfiguration {
    
    // 可以在这里定义监控模块特定的Bean配置
    
}
