package com.yyzs.common.context;

import com.yyzs.common.annotation.DataSource;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据源上下文持有者
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Slf4j
public class DataSourceContextHolder {

    private static final ThreadLocal<DataSource.DataSourceType> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置数据源类型
     *
     * @param dataSourceType 数据源类型
     */
    public static void setDataSourceType(DataSource.DataSourceType dataSourceType) {
        log.debug("Set datasource type: {}", dataSourceType);
        CONTEXT_HOLDER.set(dataSourceType);
    }

    /**
     * 获取数据源类型
     *
     * @return 数据源类型
     */
    public static DataSource.DataSourceType getDataSourceType() {
        DataSource.DataSourceType dataSourceType = CONTEXT_HOLDER.get();
        log.debug("Get datasource type: {}", dataSourceType);
        return dataSourceType;
    }

    /**
     * 清除数据源类型
     */
    public static void clearDataSourceType() {
        log.debug("Clear datasource type");
        CONTEXT_HOLDER.remove();
    }
}
