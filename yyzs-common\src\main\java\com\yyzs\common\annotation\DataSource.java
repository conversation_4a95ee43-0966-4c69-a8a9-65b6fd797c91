package com.yyzs.common.annotation;

import java.lang.annotation.*;

/**
 * 数据源注解
 * 用于标识使用哪个数据源
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataSource {
    
    /**
     * 数据源类型
     */
    DataSourceType value() default DataSourceType.PRIMARY;
    
    /**
     * 数据源类型枚举
     */
    enum DataSourceType {
        /**
         * 主数据源
         */
        PRIMARY("primaryDataSource", "primarySqlSessionTemplate", "primaryTransactionManager"),
        
        /**
         * 从数据源
         */
        SECONDARY("secondaryDataSource", "secondarySqlSessionTemplate", "secondaryTransactionManager"),
        
        /**
         * Agent数据源
         */
        AGENT("agentDataSource", "agentSqlSessionTemplate", "agentTransactionManager");
        
        private final String dataSourceName;
        private final String sqlSessionTemplateName;
        private final String transactionManagerName;
        
        DataSourceType(String dataSourceName, String sqlSessionTemplateName, String transactionManagerName) {
            this.dataSourceName = dataSourceName;
            this.sqlSessionTemplateName = sqlSessionTemplateName;
            this.transactionManagerName = transactionManagerName;
        }
        
        public String getDataSourceName() {
            return dataSourceName;
        }
        
        public String getSqlSessionTemplateName() {
            return sqlSessionTemplateName;
        }
        
        public String getTransactionManagerName() {
            return transactionManagerName;
        }
    }
}
