package com.yyzs.modules.system.mapper;

import com.yyzs.common.annotation.DataSource;
import com.yyzs.common.entity.User;
import com.yyzs.common.mapper.YYBaseMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户Mapper接口 - 简化版本
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Repository
@Mapper
public interface UserMapper extends YYBaseMapper<User> {

    /**
     * 根据用户名查询用户
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT * FROM sys_user WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 查询活跃用户列表
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT * FROM sys_user WHERE status = 1 AND deleted = 0 ORDER BY create_time DESC")
    List<User> findActiveUsers();

    /**
     * 更新用户状态
     */
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Update("UPDATE sys_user SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") Integer status);

    /**
     * 检查用户名是否存在
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT COUNT(*) FROM sys_user WHERE username = #{username} AND deleted = 0")
    int checkUsernameExists(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT COUNT(*) FROM sys_user WHERE email = #{email} AND deleted = 0")
    int checkEmailExists(@Param("email") String email);
}
