package com.yyzs.agent.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * Agent模块自动配置类
 *
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.agent.entity.AgentInfo")
@ComponentScan(basePackages = {
    "com.yyzs.agent.service",
    "com.yyzs.agent.controller",
    "com.yyzs.agent.entity",
    "com.yyzs.agent.task"
})
@MapperScan(basePackages = {
    "com.yyzs.agent.mapper"
})
@EntityScan(basePackages = {
    "com.yyzs.agent.entity"
})
public class AgentAutoConfiguration {
    
    // 可以在这里定义Agent模块特定的Bean配置
    
}
