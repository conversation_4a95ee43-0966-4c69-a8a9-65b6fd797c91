package com.yyzs.plugins.logging.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;

/**
 * Logging插件自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.plugins.logging.service.LoggingService")
@ComponentScan(basePackages = {
    "com.yyzs.plugins.logging.service",
    "com.yyzs.plugins.logging.config",
    "com.yyzs.plugins.logging.aspect"
})
public class LoggingPluginAutoConfiguration {
    
    // 可以在这里定义日志插件特定的Bean配置
    
}
