package com.yyzs.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyzs.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Agent信息实体类
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_info")
public class AgentInfo extends BaseEntity {

    /**
     * Agent唯一标识
     */
    @TableField("agent_id")
    private String agentId;

    /**
     * Agent名称
     */
    @TableField("agent_name")
    private String agentName;

    /**
     * 服务器IP地址
     */
    @TableField("server_ip")
    private String serverIp;

    /**
     * 服务器主机名
     */
    @TableField("server_hostname")
    private String serverHostname;

    /**
     * 操作系统类型
     */
    @TableField("os_type")
    private String osType;

    /**
     * 操作系统版本
     */
    @TableField("os_version")
    private String osVersion;

    /**
     * CPU核心数
     */
    @TableField("cpu_cores")
    private Integer cpuCores;

    /**
     * 内存大小(GB)
     */
    @TableField("memory_size")
    private Integer memorySize;

    /**
     * 磁盘大小(GB)
     */
    @TableField("disk_size")
    private Long diskSize;

    /**
     * Agent状态（0：离线，1：在线，2：异常）
     */
    @TableField("status")
    private Integer status;

    /**
     * Agent版本
     */
    @TableField("agent_version")
    private String agentVersion;

    /**
     * 最后心跳时间
     */
    @TableField("last_heartbeat")
    private LocalDateTime lastHeartbeat;

    /**
     * 注册时间
     */
    @TableField("register_time")
    private LocalDateTime registerTime;

    /**
     * 描述信息
     */
    @TableField("description")
    private String description;

    /**
     * 标签（JSON格式）
     */
    @TableField("tags")
    private String tags;
}
