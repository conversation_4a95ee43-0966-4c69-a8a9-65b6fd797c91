# Development Environment Configuration
spring:
  # Development DataSource Configuration (PostgreSQL)
  datasource:
    primary:
      url: ****************************************************************************************************
      username: postgres
      password: 123456
    secondary:
      url: ***************************************************************************************************
      username: postgres
      password: 123456
    agent:
      url: ***************************************************************************************************
      username: postgres
      password: 123456

  # Development JPA Configuration (PostgreSQL)
  jpa:
    hibernate:
      ddl-auto: create-drop # 开发环境自动创建表结构
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  # Development Redis Configuration
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 1 # 使用数据库1用于开发

# Development Logging Configuration
logging:
  level:
    root: info
    com.yyzs: debug
    org.springframework.security: debug
    org.springframework.web: debug
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'

# Development Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: "*" # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always

# Development YY-ZS Configuration
yyzs:
  # Development mode
  debug: true

  # Development CORS (relaxed)
  cors:
    enabled: true
    allowed-origins:
      - "*" # 开发环境允许所有源
    allow-credentials: false # 开发环境简化配置

  # Development file upload
  upload:
    path: ./dev-uploads/

  # Development security (relaxed)
  security:
    enabled: false # 开发环境可以关闭安全验证

  # Development tenant (disabled for easier development)
  tenant:
    enabled: false # 开发环境关闭多租户功能
