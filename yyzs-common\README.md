# YY-ZS Common Module

## 概述

YY-ZS Common模块是整个后端框架的核心模块，提供了多数据源支持和基于注解的MyBatis-Plus配置。

## 主要特性

### 1. 多数据源支持

框架支持多个数据源，通过注解方式在业务层指定使用哪个数据源：

- **PRIMARY**: 主数据源（默认）- 用于写操作
- **SECONDARY**: 从数据源 - 用于读操作
- **AGENT**: Agent数据源 - 用于Agent相关操作

### 2. MyBatis-Plus注解方式

完全采用注解方式进行数据库操作，不使用XML映射文件。

## 使用方法

### 1. 数据源配置

在`application.yml`中配置多个数据源：

```yaml
spring:
  datasource:
    primary:
      url: ***************************************
      username: root
      password: 123456
    secondary:
      url: **************************************
      username: root
      password: 123456
    agent:
      url: **************************************
      username: root
      password: 123456
```

### 2. 在Mapper中使用数据源注解

```java
@Repository
@Mapper
public interface UserMapper extends BaseMapper<User> {

    // 使用主数据源进行写操作
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Insert("INSERT INTO sys_user(username, email) VALUES(#{username}, #{email})")
    int insertUser(@Param("username") String username, @Param("email") String email);

    // 使用从数据源进行读操作
    @DataSource(DataSource.DataSourceType.SECONDARY)
    @Select("SELECT * FROM sys_user WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    // 使用Agent数据源
    @DataSource(DataSource.DataSourceType.AGENT)
    @Select("SELECT * FROM agent_info WHERE agent_id = #{agentId}")
    AgentInfo findAgentInfo(@Param("agentId") String agentId);
}
```

### 3. 在Service中使用数据源注解

```java
@Service
public class UserService {

    // 在类级别指定数据源
    @DataSource(DataSource.DataSourceType.PRIMARY)
    @Transactional
    public boolean createUser(User user) {
        // 所有数据库操作都会使用主数据源
        return userMapper.insert(user) > 0;
    }

    // 在方法级别指定数据源
    @DataSource(DataSource.DataSourceType.SECONDARY)
    public List<User> getActiveUsers() {
        // 使用从数据源进行查询
        return userMapper.findActiveUsers();
    }
}
```

### 4. MyBatis-Plus注解示例

#### 基本CRUD注解

```java
// 查询
@Select("SELECT * FROM sys_user WHERE id = #{id}")
User selectById(@Param("id") Long id);

// 插入
@Insert("INSERT INTO sys_user(username, email, create_time) VALUES(#{username}, #{email}, NOW())")
int insert(@Param("username") String username, @Param("email") String email);

// 更新
@Update("UPDATE sys_user SET email = #{email}, update_time = NOW() WHERE id = #{id}")
int updateEmail(@Param("id") Long id, @Param("email") String email);

// 删除
@Delete("DELETE FROM sys_user WHERE id = #{id}")
int deleteById(@Param("id") Long id);
```

#### 动态SQL注解

```java
// 条件查询
@Select("<script>" +
        "SELECT * FROM sys_user WHERE 1=1 " +
        "<if test='username != null'>" +
        "  AND username = #{username}" +
        "</if>" +
        "<if test='status != null'>" +
        "  AND status = #{status}" +
        "</if>" +
        "</script>")
List<User> findByCondition(@Param("username") String username, @Param("status") Integer status);

// 批量操作
@Insert("<script>" +
        "INSERT INTO sys_user(username, email) VALUES " +
        "<foreach collection='users' item='user' separator=','>" +
        "  (#{user.username}, #{user.email})" +
        "</foreach>" +
        "</script>")
int batchInsert(@Param("users") List<User> users);
```

## 核心组件

### 1. 数据源配置类

- `DataSourceConfig`: 配置多个数据源和对应的SqlSessionFactory
- `DynamicDataSource`: 动态数据源路由器
- `MybatisPlusConfig`: MyBatis-Plus配置

### 2. 注解和切面

- `@DataSource`: 数据源注解
- `DataSourceAspect`: 数据源切换切面
- `DataSourceContextHolder`: 数据源上下文持有者

### 3. 基础类

- `BaseEntity`: 基础实体类，包含通用字段
- `BaseMapper`: 基础Mapper接口，扩展常用注解方法

## 注意事项

1. **事务管理**: 每个数据源都有独立的事务管理器，跨数据源操作需要注意事务一致性
2. **注解优先级**: 方法级别的`@DataSource`注解优先于类级别的注解
3. **默认数据源**: 如果没有指定数据源注解，默认使用PRIMARY数据源
4. **连接池配置**: 每个数据源都有独立的连接池配置，可根据实际需求调整

## 扩展数据源

如需添加新的数据源，按以下步骤操作：

1. 在`application.yml`中添加新数据源配置
2. 在`DataSource.DataSourceType`枚举中添加新类型
3. 在`DataSourceConfig`中添加对应的Bean配置
4. 在业务代码中使用新的数据源注解

## 示例项目结构

```
common/
├── src/main/java/com/yyzs/common/
│   ├── annotation/          # 注解定义
│   │   └── DataSource.java
│   ├── aspect/              # 切面处理
│   │   └── DataSourceAspect.java
│   ├── config/              # 配置类
│   │   ├── DataSourceConfig.java
│   │   ├── DynamicDataSource.java
│   │   └── MybatisPlusConfig.java
│   ├── context/             # 上下文管理
│   │   └── DataSourceContextHolder.java
│   ├── entity/              # 实体类
│   │   ├── BaseEntity.java
│   │   └── User.java
│   ├── mapper/              # 数据访问层
│   │   ├── BaseMapper.java
│   │   └── UserMapper.java
│   └── service/             # 业务逻辑层
│       └── UserService.java
└── pom.xml
```
