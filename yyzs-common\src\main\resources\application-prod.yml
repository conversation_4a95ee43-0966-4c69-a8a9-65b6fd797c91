# Production Environment Configuration
spring:
  # Production DataSource Configuration (PostgreSQL)
  datasource:
    primary:
      url: jdbc:postgresql://${DB_HOST:prod-master-db}:${DB_PORT:5432}/yyzs_master?useUnicode=true&characterEncoding=utf8&useSSL=true
      username: ${DB_USERNAME:yyzs_user}
      password: ${DB_PASSWORD:your_secure_password}
      druid:
        initial-size: 20
        min-idle: 20
        max-active: 500
    secondary:
      url: jdbc:postgresql://${DB_HOST:prod-slave-db}:${DB_PORT:5432}/yyzs_slave?useUnicode=true&characterEncoding=utf8&useSSL=true
      username: ${DB_USERNAME:yyzs_user}
      password: ${DB_PASSWORD:your_secure_password}
      druid:
        initial-size: 10
        min-idle: 10
        max-active: 200
    agent:
      url: jdbc:postgresql://${DB_HOST:prod-agent-db}:${DB_PORT:5432}/yyzs_agent?useUnicode=true&characterEncoding=utf8&useSSL=true
      username: ${DB_USERNAME:yyzs_user}
      password: ${DB_PASSWORD:your_secure_password}
      druid:
        initial-size: 10
        min-idle: 10
        max-active: 100

  # Production JPA Configuration (PostgreSQL)
  jpa:
    hibernate:
      ddl-auto: validate # 生产环境不自动修改表结构
    show-sql: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  # Production Redis Configuration
  data:
    redis:
      host: ${REDIS_HOST:prod-redis}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:your_redis_password}
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: 5000ms
          max-idle: 10
          min-idle: 5

# Production Logging Configuration
logging:
  level:
    root: warn
    com.yyzs: info
    org.springframework.security: warn
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
  file:
    name: /var/log/yyzs/application.log
    max-size: 100MB
    max-history: 30

# Production Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# Production YY-ZS Configuration
yyzs:
  # Production mode
  debug: false

  # Production CORS (strict)
  cors:
    enabled: true
    allowed-origins:
      - "https://admin.yyzs.com"
      - "https://api.yyzs.com"
      - "https://monitor.yyzs.com"
    allow-credentials: true
    max-age: 86400 # 24 hours for production

  # Production file upload
  upload:
    path: /data/yyzs/uploads/

  # Production security (strict)
  security:
    enabled: true
    
  # JWT Configuration for production
jwt:
  secret: ${JWT_SECRET:your_very_secure_jwt_secret_key_for_production}
  expiration: 7200000 # 2 hours for production
