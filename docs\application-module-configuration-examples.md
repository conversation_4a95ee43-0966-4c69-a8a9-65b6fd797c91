# 应用模块配置示例

## 概述

本文档展示了如何在各个应用的 `pom.xml` 中配置模块依赖，以实现不同的功能组合。

## Admin Application (管理后台)

### 功能需求
- 用户管理、权限管理
- 系统监控
- 文件管理
- 通知管理
- 工作流管理
- 安全认证
- 缓存支持
- 日志记录

### pom.xml 配置
```xml
<dependencies>
    <!-- 基础依赖 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-common</artifactId>
    </dependency>
    
    <!-- 业务模块 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-system-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-monitor-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-file-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-notification-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-workflow-module</artifactId>
    </dependency>
    
    <!-- 插件 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-security-plugin</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-cache-plugin</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-logging-plugin</artifactId>
    </dependency>
</dependencies>
```

## Agent Application (Agent应用)

### 功能需求
- Agent 管理
- 组件监控
- 日志记录
- 系统集成

### pom.xml 配置
```xml
<dependencies>
    <!-- 基础依赖 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-common</artifactId>
    </dependency>
    
    <!-- Agent 模块 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-agent</artifactId>
    </dependency>
    
    <!-- 监控模块 (Agent需要监控功能) -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-monitor-module</artifactId>
    </dependency>
    
    <!-- 插件 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-logging-plugin</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-integration-plugin</artifactId>
    </dependency>
</dependencies>
```

## API Application (API应用)

### 功能需求
- RESTful API 服务
- 用户认证
- 文件服务
- 工作流 API
- Agent 接口
- 安全控制
- 缓存支持

### pom.xml 配置
```xml
<dependencies>
    <!-- 基础依赖 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-common</artifactId>
    </dependency>
    
    <!-- 业务模块 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-system-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-file-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-workflow-module</artifactId>
    </dependency>
    
    <!-- Agent 模块 (提供Agent相关API) -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-agent</artifactId>
    </dependency>
    
    <!-- 插件 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-security-plugin</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-cache-plugin</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-logging-plugin</artifactId>
    </dependency>
</dependencies>
```

## Monitor Application (监控应用)

### 功能需求
- 系统监控
- 告警通知
- 日志记录
- 系统集成

### pom.xml 配置
```xml
<dependencies>
    <!-- 基础依赖 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-common</artifactId>
    </dependency>
    
    <!-- 业务模块 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-monitor-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-notification-module</artifactId>
    </dependency>
    
    <!-- 插件 -->
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-logging-plugin</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-integration-plugin</artifactId>
    </dependency>
</dependencies>
```

## 自定义应用示例

### 轻量级 API 应用
如果只需要提供基础的用户 API：

```xml
<dependencies>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-common</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-system-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-security-plugin</artifactId>
    </dependency>
</dependencies>
```

### 纯监控应用
如果只需要监控功能：

```xml
<dependencies>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-common</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-monitor-module</artifactId>
    </dependency>
    <dependency>
        <groupId>com.yyzs</groupId>
        <artifactId>yy-zs-logging-plugin</artifactId>
    </dependency>
</dependencies>
```

## 模块依赖说明

### 核心模块
- **yy-zs-common**: 所有应用都必须依赖的基础模块
- **yy-zs-system-module**: 用户管理、权限管理等系统功能
- **yy-zs-monitor-module**: 系统监控、性能指标收集
- **yy-zs-file-module**: 文件上传、下载、管理
- **yy-zs-notification-module**: 消息通知、邮件发送
- **yy-zs-workflow-module**: 工作流引擎
- **yy-zs-agent**: Agent 管理和通信

### 插件模块
- **yy-zs-security-plugin**: 安全认证、权限控制
- **yy-zs-cache-plugin**: 缓存管理
- **yy-zs-logging-plugin**: 日志记录和管理
- **yy-zs-integration-plugin**: 第三方系统集成

## 最佳实践

### 1. 按需加载
只添加应用真正需要的模块，避免不必要的依赖。

### 2. 功能分组
将相关功能的模块组合在一起，例如：
- 管理功能：system + security + cache
- 监控功能：monitor + notification + logging
- API 功能：system + file + security

### 3. 环境差异
可以通过 Maven Profile 在不同环境中加载不同的模块：

```xml
<profiles>
    <profile>
        <id>dev</id>
        <dependencies>
            <!-- 开发环境额外的调试模块 -->
        </dependencies>
    </profile>
    <profile>
        <id>prod</id>
        <dependencies>
            <!-- 生产环境的性能监控模块 -->
        </dependencies>
    </profile>
</profiles>
```

### 4. 版本管理
在父 pom.xml 中统一管理模块版本：

```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.yyzs</groupId>
            <artifactId>yy-zs-system-module</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 其他模块... -->
    </dependencies>
</dependencyManagement>
```

## 验证配置

### 1. 编译验证
```bash
mvn clean compile
```

### 2. 启动验证
```bash
mvn spring-boot:run --debug
```

### 3. 功能验证
检查应用日志，确认所需模块都已正确加载。

通过这种配置方式，每个应用都可以灵活地选择自己需要的功能模块，实现真正的模块化和可插拔架构。
