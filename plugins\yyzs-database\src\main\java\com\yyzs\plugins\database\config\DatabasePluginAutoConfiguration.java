package com.yyzs.plugins.database.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * Database插件自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ComponentScan(basePackages = {
    "com.yyzs.plugins.database.*"
})
@EntityScan(basePackages = {
    "com.yyzs.plugins.database.entity"
})
public class DatabasePluginAutoConfiguration {
    
    // 可以在这里定义数据库插件特定的Bean配置
    
}
