package com.yyzs.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yyzs.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 组件信息实体类
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("component_info")
public class ComponentInfo extends BaseEntity {

    /**
     * 组件名称
     */
    @TableField("component_name")
    private String componentName;

    /**
     * 组件类型
     */
    @TableField("component_type")
    private String componentType;

    /**
     * 组件版本
     */
    @TableField("component_version")
    private String componentVersion;

    /**
     * 安装路径
     */
    @TableField("install_path")
    private String installPath;

    /**
     * 配置文件路径
     */
    @TableField("config_path")
    private String configPath;

    /**
     * 启动命令
     */
    @TableField("start_command")
    private String startCommand;

    /**
     * 停止命令
     */
    @TableField("stop_command")
    private String stopCommand;

    /**
     * 重启命令
     */
    @TableField("restart_command")
    private String restartCommand;

    /**
     * 状态检查命令
     */
    @TableField("status_command")
    private String statusCommand;

    /**
     * 组件状态（0：未安装，1：已安装，2：运行中，3：已停止，4：异常）
     */
    @TableField("status")
    private Integer status;

    /**
     * 所属Agent ID
     */
    @TableField("agent_id")
    private String agentId;

    /**
     * 端口号
     */
    @TableField("port")
    private Integer port;

    /**
     * 进程ID
     */
    @TableField("process_id")
    private Integer processId;

    /**
     * 最后检查时间
     */
    @TableField("last_check_time")
    private LocalDateTime lastCheckTime;

    /**
     * 安装时间
     */
    @TableField("install_time")
    private LocalDateTime installTime;

    /**
     * 启动时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 描述信息
     */
    @TableField("description")
    private String description;

    /**
     * 配置参数（JSON格式）
     */
    @TableField("config_params")
    private String configParams;
}
