package com.yyzs.modules.notification.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;

/**
 * Notification模块自动配置类
 * 
 * <AUTHOR>
 * @since 2024-08-31
 */
@AutoConfiguration
@ConditionalOnClass(name = "com.yyzs.modules.notification.entity.NotificationMessage")
@ComponentScan(basePackages = {
    "com.yyzs.modules.notification.service",
    "com.yyzs.modules.notification.controller",
    "com.yyzs.modules.notification.config"
})
@MapperScan(basePackages = {
    "com.yyzs.modules.notification.mapper"
})
public class NotificationModuleAutoConfiguration {
    
    // 可以在这里定义通知模块特定的Bean配置
    
}
