# YY-ZS Backend Framework Common Configuration
# 所有应用共享的公共配置

spring:
  # Multi-DataSource Configuration (PostgreSQL)
  datasource:
    # Primary DataSource (Master)
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: org.postgresql.Driver
      url: ************************************************************************************************
      username: postgres
      password: 123456
      druid:
        initial-size: 10
        min-idle: 10
        max-active: 200
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,wall,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

    # Secondary DataSource (Slave/Read)
    secondary:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: org.postgresql.Driver
      url: ***********************************************************************************************
      username: postgres
      password: 123456
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 100
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,wall,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

    # Agent DataSource
    agent:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: org.postgresql.Driver
      url: ***********************************************************************************************
      username: postgres
      password: 123456
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 50
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,wall,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    
    # Druid Monitor Configuration
    druid:
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin
        allow:
        deny:

  # JPA Configuration (PostgreSQL)
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          lob:
            non_contextual_creation: true

  # Redis Configuration (Optional)
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Jackson Configuration
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus Configuration (Annotation-based)
mybatis-plus:
  # 不使用XML映射文件，全部采用注解方式
  # mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.yyzs.**.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    # 开启SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty

# Logging Configuration
logging:
  level:
    com.yyzs: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# JWT Configuration
jwt:
  secret: yyzs-jwt-secret-key-2024
  expiration: 86400000 # 24 hours
  header: Authorization
  prefix: Bearer

# Common YY-ZS Configuration
yyzs:
  # CORS Configuration
  cors:
    enabled: true
    allowed-origins:
      - "http://localhost:3000"
      - "http://localhost:8080"
      - "http://localhost:8081"
      - "http://localhost:8082"
      - "http://localhost:8083"
      - "https://admin.yyzs.com"
      - "https://api.yyzs.com"
    allowed-methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
      - "PATCH"
    allowed-headers:
      - "*"
    exposed-headers:
      - "Authorization"
      - "Content-Type"
      - "X-Total-Count"
      - "X-Request-Id"
    allow-credentials: true
    max-age: 3600
    path-pattern: "/api/**"

  # Security Configuration
  security:
    enabled: true
    ignore-urls:
      - /swagger-ui/**
      - /v3/api-docs/**
      - /druid/**
      - /actuator/**

  # File Upload Configuration
  upload:
    path: /data/uploads/
    max-file-size: 10MB
    max-request-size: 50MB
    allowed-extensions: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

  # Multi-Tenant Configuration
  tenant:
    enabled: true
    tenant-id-column: tenant_id
    default-tenant-id: default
    ignore-tables:
      - sys_tenant
      - sys_tenant_package
      - sys_config
      - sys_dict_type
      - sys_dict_data
      - sys_log_login
      - sys_log_operate
