# Agent Application Configuration
spring:
  application:
    name: yy-zs-agent-app
  
  profiles:
    active: dev
    include: common

# Server Configuration
server:
  port: 8081
  servlet:
    context-path: /api/agent

# Application specific configuration
yyzs:
  app:
    name: "YY-ZS Agent Application"
    description: "Component installation, monitoring and management"
    version: "1.0.0"
  
  # Agent specific configuration
  agent:
    enabled: true
    heartbeat-interval: 30000 # 30 seconds
    max-retry-times: 3
    auto-register: true
    server-url: "http://localhost:8080/api/admin"
    
  # Component management
  component:
    install-path: "/opt/yyzs/components"
    backup-path: "/opt/yyzs/backup"
    log-path: "/opt/yyzs/logs"
    max-concurrent-operations: 5
    
  # Monitoring configuration
  monitor:
    enabled: true
    collect-interval: 60000 # 1 minute
    metrics-retention: 7 # days
    
  # Security configuration for agent
  security:
    enabled: false # Agent通常在内网环境，可以简化安全配置
    ignore-urls:
      - /api/agent/**
      - /actuator/**

# Logging configuration for agent
logging:
  level:
    com.yyzs.app.agent: debug
    com.yyzs.agent: debug
  file:
    name: logs/agent-app.log

# Management endpoints for agent
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /api/agent/actuator
  endpoint:
    health:
      show-details: always

# Scheduling configuration
spring:
  task:
    scheduling:
      pool:
        size: 10
