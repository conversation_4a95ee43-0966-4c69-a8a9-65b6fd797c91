package com.yyzs.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus自动填充处理器
 * 统一处理创建时间、更新时间、创建者、更新者等字段的自动填充
 * 
 * <AUTHOR>
 * @since 2024-08-30
 */
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充...");
        
        // 获取当前用户
        String currentUser = getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        
        // 自动填充创建时间
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
        
        // 自动填充更新时间
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
        
        // 自动填充创建者
        this.strictInsertFill(metaObject, "createBy", String.class, currentUser);
        
        // 自动填充更新者
        this.strictInsertFill(metaObject, "updateBy", String.class, currentUser);
        
        // 自动填充逻辑删除字段
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
        
        // 自动填充版本号
        this.strictInsertFill(metaObject, "version", Integer.class, 1);

        // 自动填充租户ID
        String tenantId = getCurrentTenantId();
        this.strictInsertFill(metaObject, "tenantId", String.class, tenantId);

        log.debug("插入填充完成");
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充...");
        
        // 获取当前用户
        String currentUser = getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        
        // 自动填充更新时间
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, now);
        
        // 自动填充更新者
        this.strictUpdateFill(metaObject, "updateBy", String.class, currentUser);
        
        log.debug("更新填充完成");
    }

    /**
     * 获取当前用户
     * 优先从Spring Security上下文获取，如果没有则返回系统默认值
     */
    private String getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() 
                && !"anonymousUser".equals(authentication.getPrincipal())) {
                
                // 如果是字符串类型的用户名
                if (authentication.getPrincipal() instanceof String) {
                    return (String) authentication.getPrincipal();
                }
                
                // 如果是UserDetails类型，可以根据实际情况调整
                return authentication.getName();
            }
        } catch (Exception e) {
            log.warn("获取当前用户失败: {}", e.getMessage());
        }
        
        // 默认返回系统用户
        return "system";
    }

    /**
     * 获取当前租户ID
     */
    private String getCurrentTenantId() {
        try {
            // 从租户上下文获取
            String tenantId = com.yyzs.common.handler.TenantContextHolder.getTenantId();
            if (tenantId != null) {
                return tenantId;
            }
        } catch (Exception e) {
            log.warn("获取当前租户ID失败: {}", e.getMessage());
        }

        // 默认租户ID
        return "default";
    }
}
